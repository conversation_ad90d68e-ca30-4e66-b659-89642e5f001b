import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/bottom_sheet_util.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/features/home/<USER>/home_cubit.dart';
import 'package:base_app/features/home/<USER>/build_header_widget.dart';
import 'package:base_app/features/home/<USER>/build_pick_place_widget.dart';
import 'package:base_app/features/home/<USER>/build_search_place_widget.dart';
import 'package:base_app/features/home/<USER>/recent_place_widget.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  HomeCubit homeCubit = getIt<HomeCubit>();
  @override
  void initState() {
    homeCubit.loadHistoryPlaces();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      bloc: homeCubit,
      builder: (context, state) {
        return AppBase(
          showAppBar: false,
          paddingBody: context.paddingBody,
          body: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                context.verticalSpaceCustom(context.appBarHeight * 1.5),
                BuildHeaderWidget(),
                context.verticalSpaceHigh,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Assets.lotties.go.lottie(
                      width: 40.dm,
                      height: 40.dm,
                    ),
                    context.horizontalSpaceNormal,
                    Expanded(
                      child: Text(
                        AppString.titleTransport,
                        style: AppTextStyle.regular(),
                      ),
                    ),
                  ],
                ),
                context.verticalSpaceHigh,
                BuildPickPlaceWidget(
                  title: AppString.currentLocation,
                  name: state.currentPlace?.name,
                  address: state.currentPlace?.address,
                  onTap: showStartPointPickBottomSheet,
                ),
                context.verticalSpaceHigh,
                BuildPickPlaceWidget(
                  title: AppString.destinationLocation,
                  onTap: showEndPointPickBottomSheet,
                  colorIcon: AppColors.red,
                  name: state.destinationPlace?.name,
                  address: state.destinationPlace?.address,
                ),
                context.verticalSpaceVeryHigh,
                Row(
                  children: [
                    Assets.svgs.history.svg(
                      width: 30.dm,
                      height: 30.dm,
                      colorFilter: ColorFilter.mode(
                        AppColors.honoluluBlue,
                        BlendMode.srcIn,
                      ),
                    ),
                    context.horizontalSpaceNormal,
                    Text(
                      AppString.recentPlace,
                      style: AppTextStyle.regular(),
                    ),
                  ],
                ),
                context.verticalSpaceHigh,
                RecentPlaceWidget(
                  isHistory: false,
                  historyPlaces: state.historyPlaces,
                  onTap: (place) {
                    homeCubit.setDestinationPlace(place);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showStartPointPickBottomSheet() {
    BottomSheetUtil.show(
      context: context,
      widgetCustom: BuildSearchPlaceWidget(
        title: AppString.pickUpPlace,
        onTap: (place) {
          homeCubit.setCurrentPlace(place);
          if (place.placeId != null) {
            homeCubit.getPlaceDetail(
              placeId: place.placeId!,
              isCurrentPlace: true,
            );
          }
        },
      ),
    );
  }

  void showEndPointPickBottomSheet() {
    BottomSheetUtil.show(
      context: context,
      widgetCustom: BuildSearchPlaceWidget(
        title: AppString.destinationPlace,
        onTap: (place) {
          homeCubit.setDestinationPlace(place);
          if (place.placeId != null) {
            homeCubit.getPlaceDetail(
              placeId: place.placeId!,
              isCurrentPlace: false,
            );
          }
          SafeNavigationUtil.push(
            context,
            AppRouter.bookTrip,
            extra: {
              'placeCurrentDetail': homeCubit.state.placeCurrentDetail,
              'placeDestinationDetail': homeCubit.state.placeDestinationDetail,
            },
          );
        },
      ),
    );
  }
}
