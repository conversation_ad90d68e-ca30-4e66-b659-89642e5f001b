import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/features/home/<USER>/models/place_detail_response.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class AppMapbox extends StatefulWidget {
  final PlaceDetailResponse? placeCurrentDetail;
  final PlaceDetailResponse? placeDestinationDetail;
  const AppMapbox({
    super.key,
    this.placeCurrentDetail,
    this.placeDestinationDetail,
  });

  @override
  State<AppMapbox> createState() => _AppMapboxState();
}

class _AppMapboxState extends State<AppMapbox>
    with AutomaticKeepAliveClientMixin {
  MapboxMap? mapboxMap;

  final buggyModelPosition = Position(106.6297, 10.8231);
  final carModelPosition = Position(106.6297, 10.8231);

  @override
  bool get wantKeepAlive => true;

  Future<void> _onMapCreated(MapboxMap map) async {
    debugPrint('=== onMapCreated called ===');
    mapboxMap = map;

    final pointAnnotationManager =
        await mapboxMap?.annotations.createPointAnnotationManager();

    final ByteData bytes = await rootBundle.load(Assets.svgs.location.path);
    final Uint8List imageData = bytes.buffer.asUint8List();

    print('widget.placeCurrentDetail => ${widget.placeCurrentDetail?.lat} -- ${widget.placeCurrentDetail?.lng}');

    PointAnnotationOptions pointAnnotationOptions = PointAnnotationOptions(
      geometry: Point(
          coordinates: Position(widget.placeCurrentDetail?.lng ?? 0,
              widget.placeCurrentDetail?.lat ?? 0)), // Example coordinates
      image: imageData,
      iconSize: 3.0,
    );

    // Add the annotation to the map
    pointAnnotationManager?.create(pointAnnotationOptions);

    try {
      Future.wait([
        mapboxMap!.logo.updateSettings(LogoSettings(enabled: false)),
        mapboxMap!.attribution
            .updateSettings(AttributionSettings(enabled: false)),
        mapboxMap!.scaleBar.updateSettings(ScaleBarSettings(enabled: false)),
        mapboxMap!.compass.updateSettings(CompassSettings(enabled: false)),
      ]);
    } catch (e, stackTrace) {
      debugPrint('Error: $e');
      debugPrint('Stack: $stackTrace');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        MapWidget(
          key: const ValueKey<String>('mapWidget'),
          mapOptions: MapOptions(
            pixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          cameraOptions: CameraOptions(
            center: Point(
                coordinates: Position(
              widget.placeCurrentDetail?.lng ?? 0,
              widget.placeCurrentDetail?.lat ?? 0,
            )),
            zoom: 17,
            bearing: 15,
            pitch: 55,
          ),
          onMapCreated: _onMapCreated,
          onTapListener: _onMapTap,
        ),
        Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          right: 16,
          child: AppAnimatedButton(
            onTap: () {
              SafeNavigationUtil.pop(context);
            },
            child: Container(
              padding: context.paddingNormal,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: context.borderRadiusMedium,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onMapTap(MapContentGestureContext context) {
    final coordinate = context.point;
    debugPrint(
        'Map tapped at: ${coordinate.coordinates.lat}, ${coordinate.coordinates.lng}');
  }
}
